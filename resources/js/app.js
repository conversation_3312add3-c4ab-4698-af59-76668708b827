require('./bootstrap');
import './utils/auth';

import { createApp } from 'vue';
import { createRouter, createWebHistory } from 'vue-router';
import { i18n, initializeLanguage } from './i18n';

// Import components
import App from './components/App.vue';
import Home from './components/Home.vue';
import ActivityGenerator from './components/ActivityGenerator.vue';
import ExportActivity from './components/ExportActivity.vue';
import Login from './components/Auth/Login.vue';
import Register from './components/Auth/Register.vue';
import CombinedRouteActivity from './components/CombinedRouteActivity.vue';
import Profile from './components/Profile.vue';
import PurchaseTokens from './components/Tokens/PurchaseTokens.vue';

// Define routes
const routes = [
    { path: '/', component: Home, name: 'home' },
    { path: '/generate-activity', component: ActivityGenerator, name: 'generate-activity', meta: { requiresAuth: true } },
    { path: '/export-activity', component: ExportActivity, name: 'export-activity', meta: { requiresAuth: true } },
    { path: '/create', redirect: { name: 'generate-activity' } },
    { path: '/login', component: Login, name: 'login' },
    { path: '/register', component: Register, name: 'register' },
    { path: '/combined-create', component: CombinedRouteActivity, name: 'combined-create', meta: { requiresAuth: true } },
    { path: '/profile', component: Profile, name: 'profile', meta: { requiresAuth: true } },
    { path: '/purchase-tokens', component: PurchaseTokens, name: 'purchase-tokens', meta: { requiresAuth: true } }
];

// Create the router
const router = createRouter({
    history: createWebHistory(),
    routes
});

// Navigation guard for protected routes
router.beforeEach((to, from, next) => {
    const isAuthenticated = !!localStorage.getItem('createyourrun_token');

    if (to.meta.requiresAuth && !isAuthenticated) {
        // Redirect to login if trying to access a protected route
        next({ name: 'login' });
    } else if ((to.name === 'login' || to.name === 'register') && isAuthenticated) {
        // Redirect to home if trying to access login/register while already authenticated
        next({ name: 'home' });
    } else {
        // Continue as normal
        next();
    }
});

// Make router globally available for auth utility
window.router = router;

// Initialize and mount the Vue application with i18n
async function initApp() {
    // Initialize language detection
    await initializeLanguage();

    // Create and mount the Vue application
    const app = createApp(App);
    app.use(router);
    app.use(i18n);
    app.mount('#app');
}

// Start the application
initApp().catch(console.error);

