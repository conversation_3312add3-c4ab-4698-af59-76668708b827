<template>
    <div class="flex justify-center">
        <div class="w-full max-w-md">
            <div class="bg-white p-8 rounded-lg shadow-md">
                <h1 class="text-2xl font-bold text-strava-orange mb-6 text-center">{{ $t('auth.register.title') }}</h1>

                <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
                    <p>{{ error }}</p>
                </div>

                <form @submit.prevent="register">
                    <div class="mb-4">
                        <label class="block text-strava-grayLight mb-2" for="name">{{ $t('auth.register.name') }}</label>
                        <input type="text" id="name" v-model="name" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange" required>
                    </div>

                    <div class="mb-4">
                        <label class="block text-strava-grayLight mb-2" for="email">{{ $t('auth.register.email') }}</label>
                        <input type="email" id="email" v-model="email" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange" required>
                    </div>

                    <div class="mb-4">
                        <label class="block text-strava-grayLight mb-2" for="password">{{ $t('auth.register.password') }}</label>
                        <input type="password" id="password" v-model="password" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange" required>
                    </div>

                    <div class="mb-6">
                        <label class="block text-strava-grayLight mb-2" for="password_confirmation">{{ $t('auth.register.confirmPassword') }}</label>
                        <input type="password" id="password_confirmation" v-model="passwordConfirmation" class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-strava-orange" required>
                    </div>

                    <div class="mb-6">
                        <div class="flex items-center">
                            <input type="checkbox" id="terms" v-model="acceptTerms" class="mr-2 accent-strava-orange" required>
                            <label for="terms" class="text-strava-grayMedium">{{ $t('auth.register.termsAgreement') }} <a href="#" class="text-strava-orange hover:underline">{{ $t('auth.register.termsOfService') }}</a> {{ $t('auth.register.and') }} <a href="#" class="text-strava-orange hover:underline">{{ $t('auth.register.privacyPolicy') }}</a></label>
                        </div>
                    </div>

                    <button type="submit" class="w-full bg-strava-orange text-white py-2 px-4 rounded hover:bg-strava-orangeLight">
                        {{ loading ? $t('auth.register.submitting') : $t('auth.register.submit') }}
                    </button>
                </form>

                <div class="mt-6 text-center">
                    <p class="text-strava-grayLight">{{ $t('auth.register.hasAccount') }}
                        <router-link :to="{ name: 'login' }" class="text-strava-orange hover:underline">{{ $t('auth.register.signIn') }}</router-link>
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import axios from 'axios';
import { getUser, setUser } from '../../utils/auth';

export default {
    name: 'Register',
    setup() {
        const router = useRouter();
        const { t } = useI18n();
        const name = ref('');
        const email = ref('');
        const password = ref('');
        const passwordConfirmation = ref('');
        const acceptTerms = ref(false);
        const loading = ref(false);
        const error = ref('');

        // Check if user is already logged in
        if (getUser()) {
            router.push({ name: 'home' });
            return;
        }

        const register = async () => {
            loading.value = true;
            error.value = '';

            // Basic validation
            if (password.value !== passwordConfirmation.value) {
                error.value = t('auth.errors.passwordMismatch');
                loading.value = false;
                return;
            }

            try {
                const response = await axios.post('/api/register', {
                    name: name.value,
                    email: email.value,
                    password: password.value,
                    password_confirmation: passwordConfirmation.value
                });

                const { user, token } = response.data;

                // Store the auth token and user info using our auth utility
                setUser(user, token);

                // Redirect to the home page
                router.push({ name: 'home' });
            } catch (err) {
                if (err.response && err.response.data && err.response.data.errors) {
                    const errors = err.response.data.errors;
                    error.value = Object.values(errors)[0][0]; // Get first error message
                } else {
                    error.value = t('auth.errors.registrationFailed');
                }
            } finally {
                loading.value = false;
            }
        };

        return {
            name,
            email,
            password,
            passwordConfirmation,
            acceptTerms,
            loading,
            error,
            register
        };
    }
}
</script>
