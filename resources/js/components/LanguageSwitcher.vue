<template>
    <div class="relative inline-block text-left">
        <div>
            <button
                @click="toggleDropdown"
                type="button"
                class="inline-flex items-center justify-center w-full rounded-md border border-gray-300 shadow-sm px-3 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-strava-orange"
                :aria-expanded="isOpen"
                aria-haspopup="true"
            >
                <img
                    :src="currentLanguageFlag"
                    :alt="currentLanguageLabel"
                    class="w-4 h-3 mr-2 object-cover rounded-sm"
                >
                <span class="hidden sm:inline">{{ currentLanguageLabel }}</span>
                <span class="sm:hidden">{{ currentLanguageCode.toUpperCase() }}</span>
                <svg class="-mr-1 ml-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
            </button>
        </div>

        <transition
            enter-active-class="transition ease-out duration-100"
            enter-from-class="transform opacity-0 scale-95"
            enter-to-class="transform opacity-100 scale-100"
            leave-active-class="transition ease-in duration-75"
            leave-from-class="transform opacity-100 scale-100"
            leave-to-class="transform opacity-0 scale-95"
        >
            <div
                v-show="isOpen"
                class="origin-top-right absolute right-0 mt-2 w-40 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
                role="menu"
                aria-orientation="vertical"
                aria-labelledby="menu-button"
                tabindex="-1"
            >
                <div class="py-1" role="none">
                    <button
                        v-for="language in languages"
                        :key="language.code"
                        @click="changeLanguage(language.code)"
                        class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-150"
                        :class="{ 'bg-gray-100 text-gray-900': currentLanguageCode === language.code }"
                        role="menuitem"
                        tabindex="-1"
                    >
                        <img
                            :src="language.flag"
                            :alt="language.label"
                            class="w-4 h-3 mr-3 object-cover rounded-sm"
                        >
                        <span>{{ language.label }}</span>
                        <svg
                            v-if="currentLanguageCode === language.code"
                            class="ml-auto h-4 w-4 text-strava-orange"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                        >
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
            </div>
        </transition>
    </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { languageService } from '../i18n';

export default {
    name: 'LanguageSwitcher',
    setup() {
        const { locale, t } = useI18n();
        const isOpen = ref(false);

        // Language configuration with flag images
        const languages = ref([
            {
                code: 'en',
                label: 'English',
                flag: '/images/languages/us-flag.svg'
            },
            {
                code: 'vi',
                label: 'Tiếng Việt',
                flag: '/images/languages/vi-flag.svg'
            }
        ]);

        const currentLanguageCode = computed(() => locale.value);

        const currentLanguage = computed(() =>
            languages.value.find(lang => lang.code === currentLanguageCode.value) || languages.value[0]
        );

        const currentLanguageLabel = computed(() => currentLanguage.value.label);
        const currentLanguageFlag = computed(() => currentLanguage.value.flag);

        const toggleDropdown = () => {
            isOpen.value = !isOpen.value;
        };

        const changeLanguage = (languageCode) => {
            if (languageCode !== currentLanguageCode.value) {
                // Update i18n locale
                locale.value = languageCode;

                // Save preference using language service
                languageService.setLanguage(languageCode);

                // Emit event for other components that might need to react
                window.dispatchEvent(new CustomEvent('language-changed', {
                    detail: { language: languageCode }
                }));
            }

            isOpen.value = false;
        };

        const closeDropdown = (event) => {
            if (!event.target.closest('.relative')) {
                isOpen.value = false;
            }
        };

        onMounted(() => {
            document.addEventListener('click', closeDropdown);
        });

        onUnmounted(() => {
            document.removeEventListener('click', closeDropdown);
        });

        return {
            isOpen,
            languages,
            currentLanguageCode,
            currentLanguageLabel,
            currentLanguageFlag,
            toggleDropdown,
            changeLanguage,
            t
        };
    }
};
</script>

<style scoped>
/* Additional styles for flag images */
img {
    border: 1px solid rgba(0, 0, 0, 0.1);
}
</style>
