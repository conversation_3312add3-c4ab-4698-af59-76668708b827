<template>
    <div class="bg-strava-surface min-h-screen">
        <h1 class="text-3xl font-bold text-strava-orange mb-6">Purchase Tokens</h1>

        <div class="bg-white p-6 rounded-lg shadow-md max-w-2xl mx-auto">
            <div v-if="user" class="mb-6">
                <p class="text-strava-grayMedium mb-2">Current token balance:</p>
                <p class="text-3xl font-bold text-strava-gray">{{ user.tokens }} <span class="text-xl">tokens</span></p>
            </div>

            <div v-if="message" class="p-4 mb-6" :class="messageClass">
                <p>{{ message }}</p>
            </div>

            <h2 class="text-xl font-bold text-strava-gray mb-6">Select a package:</h2>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                <div
                    v-for="(pkg, index) in packages"
                    :key="index"
                    class="border rounded-lg p-4 text-center hover:shadow-lg transition cursor-pointer"
                    :class="{ 'border-strava-orange': selectedPackage === index, 'border-gray-200': selectedPackage !== index }"
                    @click="selectPackage(index)"
                >
                    <h3 class="text-lg font-bold text-strava-gray mb-2">{{ pkg.name }}</h3>
                    <p class="text-2xl font-bold text-strava-orange mb-1">${{ pkg.price }}</p>
                    <p class="text-strava-grayLight mb-4">{{ pkg.tokens }} tokens</p>
                    <p class="text-xs text-strava-grayLight">{{ pkg.description }}</p>
                </div>
            </div>

            <!-- PayOS Payment Section -->
            <div v-if="selectedPackage !== null && !paymentCompleted" class="mb-6">
                <h3 class="text-lg font-bold text-strava-gray mb-2">Payment</h3>
                <div class="border border-gray-200 rounded-lg p-4">
                    <p class="text-strava-grayLight mb-4">Secure payment via PayOS</p>

                    <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg" v-if="packages[selectedPackage]">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-semibold">{{ packages[selectedPackage].name }} Package</span>
                            <span class="font-bold text-lg">${{ packages[selectedPackage].price }}</span>
                        </div>
                        <p class="text-sm text-gray-600">{{ packages[selectedPackage].tokens }} tokens • {{ packages[selectedPackage].description }}</p>
                    </div>

                    <div class="flex justify-center mb-4">
                        <button
                            @click="handlePayment"
                            class="bg-strava-orange text-white py-3 px-8 rounded hover:bg-strava-orangeLight text-lg font-semibold"
                            :disabled="loading"
                        >
                            {{ loading ? 'Creating Payment Link...' : 'Pay with PayOS' }}
                        </button>
                    </div>

                    <p class="text-xs text-gray-500 text-center">
                        You will be redirected to PayOS secure payment page
                    </p>
                </div>
            </div>

            <!-- Payment Success Message -->
            <div v-if="paymentCompleted" class="mb-6">
                <div class="text-center p-6 bg-green-50 border border-green-200 rounded-lg">
                    <div class="text-green-600 text-4xl mb-4">✓</div>
                    <h3 class="text-lg font-bold text-green-800 mb-2">Payment Successful!</h3>
                    <p class="text-green-700 mb-4">Your tokens have been added to your account.</p>
                    <button
                        @click="resetPayment"
                        class="bg-strava-orange text-white py-2 px-6 rounded hover:bg-strava-orangeLight"
                    >
                        Purchase More Tokens
                    </button>
                </div>
            </div>

            <!-- Payment Cancelled Message -->
            <div v-if="paymentCancelled" class="mb-6">
                <div class="text-center p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="text-yellow-600 text-4xl mb-4">⚠</div>
                    <h3 class="text-lg font-bold text-yellow-800 mb-2">Payment Cancelled</h3>
                    <p class="text-yellow-700 mb-4">Your payment was cancelled. No charges were made.</p>
                    <button
                        @click="resetPayment"
                        class="bg-strava-orange text-white py-2 px-6 rounded hover:bg-strava-orangeLight"
                    >
                        Try Again
                    </button>
                </div>
            </div>



            <!-- Default Purchase Button (when no package selected) -->
            <div v-if="selectedPackage === null && !paymentCompleted && !paymentCancelled" class="flex justify-end">
                <p class="text-strava-grayLight">Please select a package to continue</p>
            </div>
        </div>
    </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';

export default {
    name: 'PurchaseTokens',
    setup() {
        const router = useRouter();
        const user = ref(null);
        const selectedPackage = ref(null);
        const message = ref('');
        const messageClass = ref('');
        const loading = ref(false);
        const packages = ref([]);

        // Payment state
        const paymentCompleted = ref(false);
        const paymentCancelled = ref(false);

        const fetchUserData = async () => {
            try {
                const response = await axios.get('/api/user');
                user.value = response.data;
            } catch (error) {
                console.error('Error fetching user data:', error);
                // Redirect to login if not authenticated
                if (error.response && error.response.status === 401) {
                    router.push({ name: 'login' });
                }
            }
        };

        const fetchPackages = async () => {
            try {
                const response = await axios.get('/api/token-packages');
                packages.value = response.data.packages;
            } catch (error) {
                console.error('Error fetching packages:', error);
                message.value = 'Failed to load packages. Please refresh the page.';
                messageClass.value = 'text-red-600';
            }
        };

        // Check URL parameters for payment status on component mount
        const checkPaymentStatus = () => {
            const urlParams = new URLSearchParams(window.location.search);

            if (urlParams.get('success') === 'true') {
                paymentCompleted.value = true;
                message.value = 'Payment completed successfully! Your tokens have been added to your account.';
                messageClass.value = 'bg-green-100 text-green-700 border-l-4 border-green-500';

                // Refresh user data to reflect new token balance (webhook should have processed)
                setTimeout(async () => {
                    await fetchUserData();
                }, 2000);

                // Clean up URL parameters
                setTimeout(() => {
                    router.replace({ name: 'purchase-tokens' });
                }, 100);
            } else if (urlParams.get('canceled') === 'true') {
                paymentCancelled.value = true;
                message.value = 'Payment was cancelled. No charges were made.';
                messageClass.value = 'bg-yellow-100 text-yellow-700 border-l-4 border-yellow-500';

                // Clean up URL parameters
                setTimeout(() => {
                    router.replace({ name: 'purchase-tokens' });
                }, 100);
            }
        };

        onMounted(async () => {
            await fetchUserData();
            await fetchPackages();
            checkPaymentStatus();
        });

        const selectPackage = (index) => {
            selectedPackage.value = index;
            // Reset payment state when selecting a new package
            resetPaymentState();
        };

        const handlePayment = async () => {
            if (selectedPackage.value === null) return;

            loading.value = true;
            message.value = '';

            try {
                const pkg = packages.value[selectedPackage.value];
                if (!pkg) {
                    throw new Error('Selected package not found');
                }

                const response = await axios.post('/api/payos/create-payment', {
                    package_type: pkg.type
                });

                if (response.data.success) {
                    const { checkoutUrl } = response.data.data;

                    // Redirect to PayOS hosted checkout page
                    window.location.href = checkoutUrl;
                } else {
                    throw new Error(response.data.message || 'Failed to create payment link');
                }

            } catch (error) {
                console.error('Payment link creation error:', error);
                message.value = error.response?.data?.message || 'Failed to create payment link. Please try again.';
                messageClass.value = 'bg-red-100 text-red-700 border-l-4 border-red-500';
                loading.value = false;
            }
        };

        const resetPayment = () => {
            resetPaymentState();
            selectedPackage.value = null;
            message.value = '';
        };

        const resetPaymentState = () => {
            paymentCompleted.value = false;
            paymentCancelled.value = false;
        };

        return {
            user,
            packages,
            selectedPackage,
            message,
            messageClass,
            loading,
            paymentCompleted,
            paymentCancelled,
            selectPackage,
            handlePayment,
            resetPayment
        };
    }
}
</script>

<style>
/* Strava style customizations */
input:focus {
    outline: none;
    border-color: #FC4C02;
}
</style>
