{"nav": {"brand": "CreateYourRun", "generateActivity": "Generate Activity", "export": "Export", "getTokens": "Get Tokens", "profile": "Profile", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout"}, "footer": {"copyright": "© CreateYourRun. All rights reserved."}, "home": {"title": "Welcome to CreateYourRun", "subtitle": "Create virtual running activities and export them to your favorite platforms", "description": "Create authentic running and cycling routes with realistic pace variations that pass Strava's analysis algorithms.", "getStarted": "Get Started", "newFeature": {"title": "New! All-in-One Creation", "description": "Create routes and configure activities in a single step - faster and easier!", "button": "Try All-in-One Creator"}, "features": {"createRoutes": {"title": "Create Routes", "description": "Design custom running routes on an interactive map. Add waypoints, adjust the path, and create the perfect route.", "button": "Start Creating"}, "generateActivities": {"title": "Generate Activities", "description": "Turn your routes into realistic activities with customizable pace, heart rate, cadence, and other metrics.", "button": "Generate Now"}, "exportActivities": {"title": "Export Activities", "description": "Export your generated activities in various formats (GPX, TCX) to upload to Strava and other fitness platforms.", "button": "Export"}}, "whyUse": {"title": "Why Use CreateYourRun?", "reasons": ["Test new running routes virtually before trying them in real life", "Keep your training data consistent during device transitions", "Maintain your activity streak when traveling or injured", "Compare performance metrics across different routes", "Share route ideas with friends and running groups"]}}, "auth": {"login": {"title": "Login to Your Account", "email": "Email Address", "password": "Password", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password?", "submit": "<PERSON><PERSON>", "noAccount": "Don't have an account?", "signUp": "Sign up here"}, "register": {"title": "Create Your CreateYourRun Account", "name": "Full Name", "email": "Email Address", "password": "Password", "confirmPassword": "Confirm Password", "termsAgreement": "I agree to the", "termsOfService": "Terms of Service", "and": "and", "privacyPolicy": "Privacy Policy", "submit": "Create Account", "submitting": "Creating Account...", "hasAccount": "Already have an account?", "signIn": "<PERSON><PERSON>"}, "errors": {"invalidCredentials": "Invalid email or password", "emailRequired": "Email is required", "passwordRequired": "Password is required", "nameRequired": "Name is required", "passwordMismatch": "Passwords do not match", "registrationFailed": "Registration failed. Please try again."}}, "activity": {"generator": {"title": "Generate Activity", "routeCreator": "Route Creator", "searchLocation": "Search for a location...", "search": "Search", "aligning": "Aligning...", "clear": "Clear", "activitySummary": "Activity Summary", "distance": "Distance", "duration": "Duration", "avgPace": "Avg Pace", "avgHeartRate": "Avg Heart Rate", "continueToExport": "Continue to Export", "configureMessage": "Configure your activity parameters and click Generate", "activityParameters": "Activity Parameters", "activityName": "Activity Name", "activityType": "Activity Type", "run": "Run", "bike": "Bike", "hike": "Hike", "activityDate": "Activity Date", "startTime": "Start Time", "pace": "Pace (min/km)", "paceDisplay": "(This is how it will appear in Strava)", "paceVariability": "Pace Variability (%)", "avgHeartRateLabel": "Avg Heart Rate (bpm)", "cadence": "Cadence (spm)", "generate": "Generate", "createRouteFirst": "Please create a route first by adding points on the map", "alignToBikeRoutes": "Align to Bike Routes", "alignToRunningPaths": "Align to Running Paths", "alignToWalkingPaths": "Align to Walking Paths", "alignToRoad": "Align to Road", "alignToBikeTooltip": "Snap route to bike lanes, cycling paths, and bike-friendly roads using Mapbox cycling profile", "alignToRunningTooltip": "Snap route to pedestrian paths, sidewalks, and running-friendly routes using Mapbox walking profile", "alignToWalkingTooltip": "Snap route to walking paths, trails, and pedestrian-friendly routes using Mapbox walking profile", "alignToRoadTooltip": "Snap route to roads and paths using Mapbox"}, "export": {"title": "Export Activities", "noActivities": "No activities found", "download": "Download GPX", "delete": "Delete", "confirmDelete": "Are you sure you want to delete this activity?", "exported": "Activity exported successfully!", "deleted": "Activity deleted successfully!"}}, "tokens": {"purchase": {"title": "Purchase Tokens", "currentBalance": "Current token balance:", "tokens": "tokens", "selectPackage": "Select a package:", "payment": "Payment", "securePayment": "Secure payment via PayOS", "package": "Package", "payWithPayOS": "Pay with PayOS", "creatingPaymentLink": "Creating Payment Link...", "redirectMessage": "You will be redirected to PayOS secure payment page", "paymentSuccessful": "Payment Successful!", "tokensAdded": "Your tokens have been added to your account.", "purchaseMore": "Purchase More Tokens", "paymentCancelled": "Payment Cancelled", "paymentCancelledMessage": "Your payment was cancelled. No charges were made.", "tryAgain": "Try Again", "selectPackageMessage": "Please select a package to continue", "paymentCompleted": "Payment completed successfully! Your tokens have been added to your account.", "paymentCancelledStatus": "Payment was cancelled. No charges were made.", "failedToLoad": "Failed to load packages. Please refresh the page.", "paymentLinkError": "Failed to create payment link. Please try again."}}, "profile": {"title": "Profile", "name": "Name", "email": "Email", "tokenBalance": "Token Balance", "memberSince": "Member Since", "activitiesGenerated": "Activities Generated", "save": "Save Changes", "saved": "Profile updated successfully!"}, "common": {"loading": "Loading...", "error": "An error occurred", "success": "Success!", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh"}, "language": {"switch": "Switch Language", "english": "English", "vietnamese": "Tiếng <PERSON>"}}